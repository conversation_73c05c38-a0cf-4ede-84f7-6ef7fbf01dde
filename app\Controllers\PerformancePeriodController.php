<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\PerformancePeriodModel;
use App\Models\DutyInstructionsModel;
use App\Models\UserModel;

/**
 * PerformancePeriodController
 *
 * Handles CRUD operations for performance periods
 */
class PerformancePeriodController extends BaseController
{
    protected $performancePeriodModel;
    protected $dutyInstructionsModel;
    protected $userModel;
    protected $helpers = ['form', 'url'];

    public function __construct()
    {
        $this->performancePeriodModel = new PerformancePeriodModel();
        $this->dutyInstructionsModel = new DutyInstructionsModel();
        $this->userModel = new UserModel();
    }

    /**
     * Display list of performance periods
     */
    public function index()
    {
        $data = [
            'title' => 'Performance Periods',
            'performance_periods' => $this->performancePeriodModel->getAllWithDetails()
        ];

        return view('performance_period/performance_period_index', $data);
    }

    /**
     * Show form for creating new performance period
     */
    public function new()
    {
        $data = [
            'title' => 'Create Performance Period',
            'users' => $this->userModel->findAll(),
            'duty_instructions' => $this->dutyInstructionsModel->findAll()
        ];

        return view('performance_period/performance_period_create', $data);
    }

    /**
     * Create new performance period
     */
    public function create()
    {
        $rules = [
            'user_id' => 'required|integer',
            'title' => 'required|max_length[255]',
            'description' => 'permit_empty',
            'duty_instruction_id' => 'permit_empty|integer'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'user_id' => $this->request->getPost('user_id'),
            'duty_instruction_id' => $this->request->getPost('duty_instruction_id') ?: null,
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'created_by' => session()->get('user_id')
        ];

        // Handle file upload
        $file = $this->request->getFile('performance_period_file');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            $newName = $file->getRandomName();
            $file->move(ROOTPATH . 'public/uploads/performance_periods', $newName);
            $data['performance_period_filepath'] = 'public/uploads/performance_periods/' . $newName;
        }

        if ($this->performancePeriodModel->insert($data)) {
            return redirect()->to(base_url('performance-output'))->with('success', 'Performance period created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create performance period.');
        }
    }

    /**
     * Show specific performance period with KRAs
     */
    public function show($id)
    {
        $performancePeriod = $this->performancePeriodModel->getPerformancePeriodWithDetails($id);
        
        if (!$performancePeriod) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Performance period not found');
        }

        $data = [
            'title' => 'Performance Period Details',
            'performance_period' => $performancePeriod
        ];

        return view('performance_period/performance_period_show', $data);
    }

    /**
     * Show form for editing performance period
     */
    public function edit($id)
    {
        $performancePeriod = $this->performancePeriodModel->find($id);
        
        if (!$performancePeriod) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Performance period not found');
        }

        $data = [
            'title' => 'Edit Performance Period',
            'performance_period' => $performancePeriod,
            'users' => $this->userModel->findAll(),
            'duty_instructions' => $this->dutyInstructionsModel->findAll()
        ];

        return view('performance_period/performance_period_edit', $data);
    }

    /**
     * Update performance period
     */
    public function update($id)
    {
        $performancePeriod = $this->performancePeriodModel->find($id);
        
        if (!$performancePeriod) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Performance period not found');
        }

        $rules = [
            'user_id' => 'required|integer',
            'title' => 'required|max_length[255]',
            'description' => 'permit_empty',
            'duty_instruction_id' => 'permit_empty|integer'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'user_id' => $this->request->getPost('user_id'),
            'duty_instruction_id' => $this->request->getPost('duty_instruction_id') ?: null,
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'updated_by' => session()->get('user_id')
        ];

        // Handle file upload
        $file = $this->request->getFile('performance_period_file');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            // Delete old file if exists
            if ($performancePeriod['performance_period_filepath'] && file_exists(ROOTPATH . $performancePeriod['performance_period_filepath'])) {
                unlink(ROOTPATH . $performancePeriod['performance_period_filepath']);
            }
            
            $newName = $file->getRandomName();
            $file->move(ROOTPATH . 'public/uploads/performance_periods', $newName);
            $data['performance_period_filepath'] = 'public/uploads/performance_periods/' . $newName;
        }

        if ($this->performancePeriodModel->update($id, $data)) {
            return redirect()->to(base_url('performance-output'))->with('success', 'Performance period updated successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update performance period.');
        }
    }

    /**
     * Delete performance period
     */
    public function delete($id)
    {
        $performancePeriod = $this->performancePeriodModel->find($id);
        
        if (!$performancePeriod) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Performance period not found');
        }

        $data = [
            'deleted_by' => session()->get('user_id')
        ];

        if ($this->performancePeriodModel->update($id, $data) && $this->performancePeriodModel->delete($id)) {
            return redirect()->to(base_url('performance-output'))->with('success', 'Performance period deleted successfully.');
        } else {
            return redirect()->back()->with('error', 'Failed to delete performance period.');
        }
    }

    /**
     * Update performance period status
     */
    public function updateStatus($id)
    {
        $status = $this->request->getPost('status');
        $remarks = $this->request->getPost('status_remarks');

        if ($this->performancePeriodModel->updateStatus($id, $status, $remarks)) {
            return redirect()->back()->with('success', 'Status updated successfully.');
        } else {
            return redirect()->back()->with('error', 'Failed to update status.');
        }
    }
}

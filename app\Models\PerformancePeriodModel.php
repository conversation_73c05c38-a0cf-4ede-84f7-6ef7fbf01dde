<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * PerformancePeriodModel
 *
 * Handles database operations for the performance_period table
 * which represents performance periods for users
 */
class PerformancePeriodModel extends Model
{
    protected $table            = 'performance_period';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;

    // Fields that can be set during save/insert/update
    protected $allowedFields    = [
        'user_id',
        'duty_instruction_id',
        'title',
        'description',
        'performance_period_filepath',
        'status',
        'status_by',
        'status_at',
        'status_remarks',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation rules
    protected $validationRules = [
        'user_id'                       => 'required|integer',
        'duty_instruction_id'           => 'permit_empty|integer',
        'title'                         => 'required|max_length[255]',
        'description'                   => 'permit_empty',
        'performance_period_filepath'   => 'permit_empty|max_length[500]',
        'status'                        => 'permit_empty|max_length[50]',
        'status_by'                     => 'permit_empty|integer',
        'status_at'                     => 'permit_empty|valid_date',
        'status_remarks'                => 'permit_empty',
        'created_by'                    => 'permit_empty|integer',
        'updated_by'                    => 'permit_empty|integer',
        'deleted_by'                    => 'permit_empty|integer'
    ];

    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $beforeInsert = ['setDefaultStatus'];
    protected $afterInsert  = [];
    protected $beforeUpdate = [];
    protected $afterUpdate  = [];
    protected $beforeDelete = [];
    protected $afterDelete  = [];

    /**
     * Set default status for new performance periods
     */
    protected function setDefaultStatus(array $data)
    {
        if (!isset($data['data']['status'])) {
            $data['data']['status'] = 'pending';
            $data['data']['status_at'] = date('Y-m-d H:i:s');
            $data['data']['status_by'] = session()->get('user_id') ?? null;
        }

        return $data;
    }

    /**
     * Get all performance periods for a specific user
     *
     * @param int $userId
     * @return array
     */
    public function getByUser($userId)
    {
        return $this->where('user_id', $userId)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get performance periods by status
     *
     * @param string $status
     * @return array
     */
    public function getByStatus($status)
    {
        return $this->where('status', $status)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get performance period with user and duty instruction details
     *
     * @param int $id
     * @return array|null
     */
    public function getPerformancePeriodWithDetails($id)
    {
        $db = \Config\Database::connect();

        $query = $db->table('performance_period pp')
            ->select('pp.*,
                     CONCAT(u1.fname, " ", u1.lname) as user_name,
                     CONCAT(u2.fname, " ", u2.lname) as created_by_name,
                     CONCAT(u3.fname, " ", u3.lname) as updated_by_name,
                     CONCAT(u4.fname, " ", u4.lname) as status_by_name,
                     di.duty_instruction_title,
                     di.duty_instruction_number')
            ->join('users u1', 'pp.user_id = u1.id', 'left')
            ->join('users u2', 'pp.created_by = u2.id', 'left')
            ->join('users u3', 'pp.updated_by = u3.id', 'left')
            ->join('users u4', 'pp.status_by = u4.id', 'left')
            ->join('duty_instructions di', 'pp.duty_instruction_id = di.id', 'left')
            ->where('pp.id', $id)
            ->where('pp.deleted_at', null)
            ->get();

        return $query->getRowArray();
    }

    /**
     * Get all performance periods with user details
     *
     * @return array
     */
    public function getAllWithDetails()
    {
        $db = \Config\Database::connect();

        $query = $db->table('performance_period pp')
            ->select('pp.*,
                     CONCAT(u1.fname, " ", u1.lname) as user_name,
                     CONCAT(u2.fname, " ", u2.lname) as created_by_name,
                     CONCAT(u3.fname, " ", u3.lname) as status_by_name,
                     di.duty_instruction_title')
            ->join('users u1', 'pp.user_id = u1.id', 'left')
            ->join('users u2', 'pp.created_by = u2.id', 'left')
            ->join('users u3', 'pp.status_by = u3.id', 'left')
            ->join('duty_instructions di', 'pp.duty_instruction_id = di.id', 'left')
            ->where('pp.deleted_at', null)
            ->orderBy('pp.created_at', 'DESC')
            ->get();

        return $query->getResultArray();
    }

    /**
     * Update performance period status
     *
     * @param int $id
     * @param string $status
     * @param string $remarks
     * @return bool
     */
    public function updateStatus($id, $status, $remarks = '')
    {
        $data = [
            'status' => $status,
            'status_at' => date('Y-m-d H:i:s'),
            'status_by' => session()->get('user_id') ?? null,
            'status_remarks' => $remarks,
            'updated_by' => session()->get('user_id') ?? null
        ];

        return $this->update($id, $data);
    }

    /**
     * Get performance periods count by status
     *
     * @return array
     */
    public function getStatusCounts()
    {
        $db = \Config\Database::connect();

        $query = $db->table('performance_period')
            ->select('status, COUNT(*) as count')
            ->where('deleted_at', null)
            ->groupBy('status')
            ->get();

        return $query->getResultArray();
    }

    /**
     * Search performance periods
     *
     * @param string $searchTerm
     * @return array
     */
    public function search($searchTerm)
    {
        return $this->like('title', $searchTerm)
                    ->orLike('description', $searchTerm)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get performance periods by duty instruction
     *
     * @param int $dutyInstructionId
     * @return array
     */
    public function getByDutyInstruction($dutyInstructionId)
    {
        return $this->where('duty_instruction_id', $dutyInstructionId)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }
}
